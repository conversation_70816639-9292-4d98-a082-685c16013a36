import 'package:flutter/material.dart';
import 'package:melodyze/core/config/config.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';
import 'package:melodyze/core/ui/molecules/lyric_viewer/model/lyrics_data.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/modules/profile/bloc/profile_bloc.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/utilities/utils/utils.dart';
import 'package:melodyze/core/ui/molecules/lyric_viewer/video_screen_lyrics_viewer.dart';
import 'package:melodyze/core/services/lyrics_service.dart';

class DraftRecordingPlayerWidget extends StatefulWidget {
  final RecordingModel recording;
  final int playbackPositionMillis;
  final int audioDurationMillis;
  final bool isPlaying;

  const DraftRecordingPlayerWidget({
    super.key,
    required this.recording,
    required this.playbackPositionMillis,
    required this.audioDurationMillis,
    required this.isPlaying,
  });

  @override
  State<DraftRecordingPlayerWidget> createState() => _DraftRecordingPlayerWidgetState();
}

class _DraftRecordingPlayerWidgetState extends State<DraftRecordingPlayerWidget> {
  LyricsData? _lyricsData;
  bool _lyricsLoaded = false;

  String get songtitle => FileUtils.fromSnakeCase(widget.recording.title.split('-').first);
  String get singer => widget.recording.singer.isNotEmpty ? widget.recording.singer : ' ';
  bool get isPublishedRecording => widget.recording.isPublished;

  @override
  void initState() {
    super.initState();
    _fetchLyrics();
  }

  Future<void> _fetchLyrics() async {
    if (_lyricsLoaded) return;

    final lyricsData = await LyricsService.fetchLyrics(widget.recording.lyricsJsonPath);
    if (lyricsData != null && mounted) {
      setState(() {
        _lyricsData = lyricsData;
        _lyricsLoaded = true;
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final lyricsList = _lyricsData?.lyrics.data.map((e) => e.text).toList() ?? [];

    // Get user profile information from ProfileBloc via DI
    final profileBloc = DI().resolve<ProfileBloc>();
    final user = profileBloc.melodyzeUser;
    final userProfileImageUrl = user.profilePicUrl ?? Config.noUserDP;
    final username = 'FT. ${user.username.split(' ').first}';

    return MediaQuery(
      data: MediaQuery.of(context).copyWith(textScaler: TextScaler.noScaling),
      child: ColoredBox(
        color: Colors.black,
        child: _buildPlayerContent(context, lyricsList, userProfileImageUrl, username),
      ),
    );
  }

  Widget _buildPlayerContent(BuildContext context, List<String> lyrics, String userProfileImageUrl, String username) {
    return Stack(
      children: [
        // Main content - positioned naturally within the padded container
        Positioned.fill(
          child: Column(
            children: [
              // Header section with profile info and song metadata
              _buildHeaderSection(userProfileImageUrl, username),
              const SizedBox(height: 40),

              // Wave animation with centered play/pause button
              _buildWavePlayerSection(),

              // Lyrics section - positioned a bit lower
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(top: 110.0, bottom: 30.0),
                  child: VideoScreenLyricsViewer(
                    lyrics: lyrics,
                    lyricsData: _lyricsData?.lyrics,
                    playbackPositionMillis: widget.playbackPositionMillis,
                    audioDurationMillis: widget.audioDurationMillis,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHeaderSection(String userProfileImageUrl, String username) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(36.0, 65.0, 32.0, 28.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Profile image stack
          _buildProfileImage(userProfileImageUrl),
          const SizedBox(width: 20),
          // Recording info with song metadata
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    SizedBox(
                      width: 155,
                      child: Text(
                        songtitle,
                        style: AppTextStyles.textEthnocentricStyle.copyWith(
                          fontSize: 14,
                          color: Colors.white,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.clip,
                      ),
                    ),
                  ],
                ),
                if (singer.isNotEmpty)
                  SizedBox(
                    width: 120,
                    child: Text(
                      singer,
                      style: AppTextStyles.text14regular.copyWith(
                        fontFamily: AppFonts.iceland,
                        color: Colors.grey[400],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                const SizedBox(height: 10),
                Text(
                  username,
                  style: AppTextStyles.text16regular.copyWith(
                    fontFamily: AppFonts.iceland,
                    color: Colors.grey[400],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileImage(String userProfileImageUrl) {
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        SizedBox(
          width: 95,
          height: 95,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Image.asset(
                AssetPaths.profileBorder,
                fit: BoxFit.cover,
              ),
              ClipOval(
                child: SizedBox(
                  width: 90,
                  height: 90,
                  child: ImageLoader.network(
                    userProfileImageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[800],
                        child: const Icon(Icons.person, color: Colors.white, size: 48),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
        Positioned(
          right: -4, // Shift 4px more to the right
          bottom: -4, // Shift 4px more to the bottom
          child: SizedBox(
            width: 34,
            height: 34,
            child: Image.asset(
              AssetPaths.catComplete,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildWavePlayerSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 6.0),
      child: SizedBox(
        height: 100,
        child: widget.isPlaying
            ? Image.asset(
                '${AssetPaths.gifPath}/wave_video.gif',
                fit: BoxFit.fill,
                width: double.infinity,
                height: 100,
              )
            : Image.asset(
                '${AssetPaths.pngPath}/wave_static.png',
                fit: BoxFit.fill,
                width: double.infinity,
                height: 100,
              ),
      ),
    );
  }
}
