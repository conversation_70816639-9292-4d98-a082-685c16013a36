part of 'video_player_bloc.dart';

abstract class VideoState extends BlocState {
  const VideoState();
}

class VideoInitialState extends VideoState {
  @override
  List<Object?> get props => [];
}

class VideoLoadingState extends VideoState {
  @override
  List<Object?> get props => [];
}

class VideoLoadedState extends VideoState {
  @override
  List<Object?> get props => [];
}

class VideoPlayingState extends VideoState {
  final Duration position;
  
  const VideoPlayingState({this.position = Duration.zero});
  
  @override
  List<Object?> get props => [position];
}

class VideoPausedState extends VideoState {
  final Duration position;
  
  const VideoPausedState({this.position = Duration.zero});
  
  @override
  List<Object?> get props => [position];
}

class VideoCurrentPositionState extends VideoState {
  final Duration position;
  const VideoCurrentPositionState(this.position);
  @override
  List<Object?> get props => [position];
}

class VideoErrorState extends VideoState {
  final String error;
  const VideoErrorState(this.error);

  @override
  List<Object?> get props => [];
}
