import 'package:auto_route/auto_route.dart';
import 'package:better_player_plus/better_player_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/generic_bloc/events.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/ui/atom/app_circular_progress_indicator.dart';
import 'package:melodyze/core/ui/atom/app_slider.dart';
import 'package:melodyze/core/ui/atom/marquee.dart';
import 'package:melodyze/core/ui/molecules/app_search_bar.dart';
import 'package:melodyze/core/ui/molecules/auto_fade_widget.dart';
import 'package:melodyze/core/ui/molecules/butons/app_button.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/utilities/utils/extensions.dart';
import 'package:melodyze/core/utilities/utils/utils.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/app_toast.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/modules/home/<USER>/song_model/song_model.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';
import 'package:melodyze/modules/profile/ui/widget/recording_popup_menu.dart';
import 'package:melodyze/modules/song/bloc/song_bloc.dart';
import 'package:melodyze/modules/song/repo/song_repo.dart';
import 'package:melodyze/modules/song/service/song_service.dart';
import 'package:melodyze/modules/video_player/demo_cover_screen.dart';
import 'package:melodyze/modules/video_player/video_player_bloc/video_player_bloc.dart';
import 'package:melodyze/modules/video_player/draft_recording_screen.dart';

@RoutePage()
class VideoPlayerFullScreen extends StatefulWidget {
  final bool showPersonalizationButton;
  final bool isReelScreen;
  final RecordingModel recording;

  const VideoPlayerFullScreen({
    super.key,
    required this.showPersonalizationButton,
    this.isReelScreen = false,
    required this.recording,
  });

  @override
  State<VideoPlayerFullScreen> createState() => _VideoPlayerFullScreenState();
}

class _VideoPlayerFullScreenState extends State<VideoPlayerFullScreen> {
  SongBloc? _songBloc;

  String get info => widget.isReelScreen
      ? "${FileUtils.fromSnakeCase(widget.recording.title.split('-').first)} · ${widget.recording.singer}"
      : '${widget.recording.scale} · ${widget.recording.tempo} bpm';

  bool get showSongDetails => widget.recording.feedType != 'direct_upload';
  bool get showShareButton => !widget.isReelScreen && widget.recording.isPublished;
  bool get disableVideo => widget.recording.finalVideoFilePath.isNullOrEmpty;

  @override
  void dispose() {
    _songBloc?.close();
    super.dispose();
  }

  SongBloc _getSongBloc() {
    return _songBloc ??= SongBloc(
      songId: widget.recording.masterSongId,
      songRepo: SongRepo(
        songService: SongService(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        BlocConsumer<VideoPlayerBloc, VideoState>(
          listenWhen: (previous, current) => current is VideoErrorState,
          listener: (context, state) {
            if (state is VideoErrorState) {
              DI().resolve<AppToast>().showToast(state.error);
            }
          },
          buildWhen: (previous, current) => current is! VideoErrorState,
          builder: (context, state) {
            if (disableVideo) {
              return BlocBuilder<VideoPlayerBloc, VideoState>(
                buildWhen: (previous, current) => current is VideoCurrentPositionState || current is VideoPlayingState || current is VideoPausedState,
                builder: (context, videoState) {
                  final videoBloc = context.read<VideoPlayerBloc>();
                  final position = videoState is VideoCurrentPositionState
                      ? videoState.position.inMilliseconds
                      : videoState is VideoPausedState
                          ? videoState.position.inMilliseconds
                          : videoState is VideoPlayingState
                              ? videoState.position.inMilliseconds
                              : 0;
                  final duration = videoBloc.controller?.videoPlayerController?.value.duration?.inMilliseconds ?? 0;
                  final isPlaying = videoState is VideoPlayingState || (videoState is VideoCurrentPositionState && (videoBloc.controller?.isPlaying() ?? false));

                  return SizedBox(
                    width: double.infinity,
                    height: double.infinity,
                    child: widget.isReelScreen && widget.recording.feedType == 'demo_cover'
                        ? DemoCoverPlayerWidget(
                            recording: widget.recording,
                            playbackPositionMillis: position,
                            audioDurationMillis: duration,
                            isPlaying: isPlaying,
                          )
                        : DraftRecordingPlayerWidget(
                            recording: widget.recording,
                            playbackPositionMillis: position,
                            audioDurationMillis: duration,
                            isPlaying: isPlaying,
                          ),
                  );
                },
              );
            }

            if (state is VideoLoadingState) {
              return const Center(
                child: AppCircularProgressIndicator(),
              );
            } else if (state is VideoPlayingState || state is VideoLoadedState || state is VideoPausedState) {
              final controller = context.read<VideoPlayerBloc>().controller;
              if (controller == null) {
                throw Exception('controller is null');
              }
              return Positioned.fill(
                child: AspectRatio(
                  aspectRatio: VideoPlayerBloc.aspectRatio,
                  child: BetterPlayer(key: Key(widget.recording.id), controller: controller),
                ),
              );
            }
            return const SizedBox.shrink();
          },
        ),
        // Bottom black gradient - always visible for song metadata background
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Container(
            height: 120,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Colors.transparent, Colors.black],
              ),
            ),
          ),
        ),
        Positioned.fill(
          child: BlocBuilder<VideoPlayerBloc, VideoState>(
            builder: (context, state) {
              return AutoFadeWidget(
                stopAutoFadeOnInteraction: state is VideoPausedState,
                child: Stack(
                  children: [
                    Center(
                      child: BlocBuilder<VideoPlayerBloc, VideoState>(
                        buildWhen: (previous, current) => current is VideoPlayingState || current is VideoPausedState,
                        builder: (context, state) {
                          return AnimatedSwitcher(
                            duration: const Duration(milliseconds: 250),
                            transitionBuilder: (child, animation) {
                              return FadeTransition(
                                opacity: animation,
                                child: child,
                              );
                            },
                            child: IconButton(
                              key: ValueKey(state is VideoPlayingState ? 'pause' : 'play'),
                              icon: ImageLoader.fromAsset(
                                state is VideoPlayingState ? AssetPaths.pauseReel : AssetPaths.playReel,
                              ),
                              onPressed: context.read<VideoPlayerBloc>().togglePlay,
                            ),
                          );
                        },
                      ),
                    ),
                    if (widget.isReelScreen)
                      Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            padding: const EdgeInsets.only(left: 42, right: 42, top: 24),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [Colors.black, Colors.transparent],
                              ),
                            ),
                            child: GestureDetector(
                              onTap: () {
                                context.pushRoute(SearchRoute());
                              },
                              behavior: HitTestBehavior.opaque,
                              child: AppSearchBar(
                                backgroundColor: Colors.transparent,
                                enabled: false,
                              ),
                            ),
                          ),
                          Spacer(),
                        ],
                      ),
                  ],
                ),
              );
            },
          ),
        ),
        // Song details - moved outside of AutoFadeWidget to make it always visible
        Positioned(
          bottom: 32,
          left: 16,
          right: 16,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (showSongDetails)
                Row(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: SizedBox(
                        height: 40,
                        width: 40,
                        child: widget.recording.thumbnailPath.isEmpty
                            ? Image.asset(
                                'assets/logo/logo.png',
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) => Container(
                                  color: Colors.grey[800],
                                  child: Icon(Icons.music_note, color: Colors.white, size: 24),
                                ),
                              )
                            : ImageLoader.cachedNetworkImage(widget.recording.thumbnailPath),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.recording.genre,
                            style: AppTextStyles.text16regular.copyWith(
                              fontFamily: AppFonts.inter,
                              color: AppColors.white.withValues(alpha: 0.8),
                            ),
                          ),
                          const SizedBox(height: 2),
                          Marqueee(
                            text: info,
                            style: AppTextStyles.text14regular.copyWith(
                              fontFamily: AppFonts.iceland,
                            ),
                            width: 160,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ),
        Positioned(
          bottom: 0,
          width: MediaQuery.sizeOf(context).width,
          child: BlocBuilder<VideoPlayerBloc, VideoState>(
            buildWhen: (previous, current) =>
                current is VideoLoadedState || current is VideoErrorState || current is VideoCurrentPositionState || current is VideoPausedState || current is VideoPlayingState,
            builder: (context, state) {
              if (state is VideoErrorState) {
                return AppSlider(
                  value: 0,
                  max: 100,
                  onChanged: (value) {},
                );
              }

              final videoBloc = context.read<VideoPlayerBloc>();

              return AppSlider(
                value: state is VideoCurrentPositionState
                    ? state.position.inMilliseconds.toDouble()
                    : state is VideoPausedState
                        ? state.position.inMilliseconds.toDouble()
                        : state is VideoPlayingState
                            ? state.position.inMilliseconds.toDouble()
                            : 0.0,
                max: videoBloc.controller?.videoPlayerController?.value.duration?.inMilliseconds.toDouble() ?? 0,
                onChanged: (value) {
                  videoBloc.add(VideoSeekEvent(milliseconds: value));
                },
                gestureHitAreaHeight: 12,
                activeColor: AppColors.white,
                isRounded: false,
              );
            },
          ),
        ),

        if (widget.showPersonalizationButton && showSongDetails)
          BlocBuilder<SongBloc, BlocState>(
            bloc: _getSongBloc(),
            buildWhen: (previous, current) => current is BlocSuccessState<SongModel>,
            builder: (context, state) {
              return Positioned(
                bottom: 32,
                right: 20,
                child: SizedBox(
                  height: 40,
                  child: AppButton(
                    text: 'Cover',
                    isLoading: false,
                    iconLeft: AssetPaths.microphone,
                    innerPadding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 8.0),
                    textStyle: AppTextStyles.textOutfit900.copyWith(
                      fontSize: AppTextStyles.textOutfit900.fontSize, // Force specific size
                    ),
                    gradient: AppGradients.gradientBlackPurpleTealBorder,
                    borderSize: 2,
                    onPressed: () {
                      // Only load data when Cover button is pressed
                      if (state is! BlocSuccessState<SongModel>) {
                        _getSongBloc().add(LoadDataEvent());
                      } else {
                        context.read<VideoPlayerBloc>().add(VideoPauseEvent());
                        final songData = state;
                        context.pushRoute(
                          SongPersonalizationRoute(
                            song: songData.data,
                            defaultGenre: widget.recording.genre,
                          ),
                        );
                      }
                    },
                  ),
                ),
              );
            },
          ),

        // Share button - positioned on the right side above Cover button
        if (showShareButton)
          Positioned(
            bottom: 100,
            right: 10,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: 44,
                  height: 44,
                  child: IconButton(
                    onPressed: () => shareRecording(context, widget.recording.id),
                    icon: ImageLoader.fromAsset(
                      color: Colors.white,
                      '${AssetPaths.svgPath}/share.svg',
                      height: 32,
                      width: 32,
                    ),
                  ),
                ),
                Text(
                  'Share',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}
