import 'package:flutter/material.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';
import 'package:melodyze/core/ui/molecules/lyric_viewer/model/lyrics_data.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/utilities/utils/utils.dart';
import 'package:melodyze/core/ui/molecules/lyric_viewer/video_screen_lyrics_viewer.dart';
import 'package:melodyze/core/services/lyrics_service.dart';

class DemoCoverPlayerWidget extends StatefulWidget {
  final RecordingModel recording;
  final int playbackPositionMillis;
  final int audioDurationMillis;
  final bool isPlaying;

  const DemoCoverPlayerWidget({
    super.key,
    required this.recording,
    required this.playbackPositionMillis,
    required this.audioDurationMillis,
    required this.isPlaying,
  });

  @override
  State<DemoCoverPlayerWidget> createState() => _DemoCoverPlayerWidgetState();
}

class _DemoCoverPlayerWidgetState extends State<DemoCoverPlayerWidget> {
  LyricsData? _lyricsData;
  bool _lyricsLoaded = false;

  String get songtitle => FileUtils.fromSnakeCase(widget.recording.title.split('-').first);
  String get singer => widget.recording.singer.isNotEmpty ? widget.recording.singer : ' ';
  bool get isPublishedRecording => widget.recording.isPublished;

  @override
  void initState() {
    super.initState();
    _fetchLyrics();
  }

  Future<void> _fetchLyrics() async {
    if (_lyricsLoaded) return;

    final lyricsData = await LyricsService.fetchLyrics(widget.recording.lyricsJsonPath);
    if (lyricsData != null && mounted) {
      setState(() {
        _lyricsData = lyricsData;
        _lyricsLoaded = true;
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final lyricsList = _lyricsData?.lyrics.data.map((e) => e.text).toList() ?? [];

    return MediaQuery(
      data: MediaQuery.of(context).copyWith(textScaler: TextScaler.noScaling),
      child: ColoredBox(
        color: Colors.black,
        child: _buildPlayerContent(context, lyricsList),
      ),
    );
  }

  Widget _buildPlayerContent(BuildContext context, List<String> lyrics) {
    return Stack(
      children: [
        // Main content - positioned naturally within the padded container
        Positioned.fill(
          child: Column(
            children: [
              const SizedBox(height: 40),
              _buildVideoPlayerSection(),
              const SizedBox(height: 40),
              // Lyrics section - positioned a bit lower
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(top: 110.0, bottom: 30.0),
                  child: VideoScreenLyricsViewer(
                    lyrics: lyrics,
                    lyricsData: _lyricsData?.lyrics,
                    playbackPositionMillis: widget.playbackPositionMillis,
                    audioDurationMillis: widget.audioDurationMillis,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildVideoPlayerSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 6.0),
      child: SizedBox(
        height: 200,
        child: Container(
          width: double.infinity,
          height: 200,
          decoration: BoxDecoration(
            color: Colors.grey[900],
            borderRadius: BorderRadius.circular(8.0),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8.0),
            child: widget.isPlaying
                ? Image.asset(
                    '${AssetPaths.gifPath}/demo_loop_1.gif',
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: double.infinity,
                  )
                : Image.asset(
                    '${AssetPaths.pngPath}/demo_loop_static_1.png',
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: double.infinity,
                  ),
          ),
        ),
      ),
    );
  }
}
