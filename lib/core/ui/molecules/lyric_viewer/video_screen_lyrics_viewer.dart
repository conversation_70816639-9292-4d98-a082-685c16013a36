import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/molecules/lyric_viewer/model/lyrics_data.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';

class VideoScreenLyricsViewer extends StatefulWidget {
  final List<String> lyrics;
  final LyricsList? lyricsData;
  final int playbackPositionMillis;
  final int audioDurationMillis;

  const VideoScreenLyricsViewer({
    super.key,
    required this.lyrics,
    required this.lyricsData,
    required this.playbackPositionMillis,
    required this.audioDurationMillis,
  });

  @override
  State<VideoScreenLyricsViewer> createState() => _VideoScreenLyricsViewerState();
}

class _VideoScreenLyricsViewerState extends State<VideoScreenLyricsViewer> {
  late ScrollController _scrollController;
  int _previousLyricIndex = 0;

  // Dynamic heights based on content
  static const double _minCurrentLineHeight = 50.0; // Minimum height for current line
  static const double _otherLineHeight = 32.0; // Fixed height for prev/next lines
  static const double _currentLinePadding = 8.0; // Reduced padding for current line
  static const double _otherLinePadding = 4.0; // Minimal padding for prev/next lines

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  int _parseTimeToMillis(String time) {
    final parts = time.split(':');
    if (parts.length == 3) {
      final min = int.tryParse(parts[0]) ?? 0;
      final sec = int.tryParse(parts[1]) ?? 0;
      final ms = int.tryParse(parts[2]) ?? 0;
      return min * 60000 + sec * 1000 + ms;
    }
    return 0;
  }

  int _getCurrentLyricIndex() {
    if (widget.lyricsData == null || widget.lyrics.isEmpty) return 0;

    final data = widget.lyricsData!.data;
    if (data.isEmpty) return 0;

    // Handle looping: get position within the lyrics duration
    int adjustedPosition = widget.playbackPositionMillis;
    if (widget.audioDurationMillis > 0) {
      // Calculate lyrics duration
      final lastLyricEnd = data.isNotEmpty ? _parseTimeToMillis(data.last.endTime) : 0;
      final lyricsDuration = lastLyricEnd > 0 ? lastLyricEnd : widget.audioDurationMillis;

      // Get position within one loop cycle
      adjustedPosition = widget.playbackPositionMillis % lyricsDuration;
    }

    // Find current lyric based on timing
    for (int i = 0; i < data.length; i++) {
      final start = _parseTimeToMillis(data[i].startTime);
      final end = _parseTimeToMillis(data[i].endTime);

      if (end == 0) {
        // No end time specified, check if we're past start
        if (adjustedPosition >= start) {
          // Check if this is the last lyric or if next lyric hasn't started
          if (i == data.length - 1) {
            _scrollToLyric(i);
            return i;
          }
          final nextStart = _parseTimeToMillis(data[i + 1].startTime);
          if (adjustedPosition < nextStart) {
            _scrollToLyric(i);
            return i;
          }
        }
      } else {
        // End time specified
        if (adjustedPosition >= start && adjustedPosition < end) {
          _scrollToLyric(i);
          return i;
        }
      }
    }

    // Default fallback
    final defaultIndex = adjustedPosition < _parseTimeToMillis(data[0].startTime) ? 0 : data.length - 1;
    _scrollToLyric(defaultIndex);
    return defaultIndex;
  }

  bool _isInIdleTime() {
    if (widget.lyricsData == null || widget.lyricsData!.data.isEmpty) return false;

    final firstLyricStart = _parseTimeToMillis(widget.lyricsData!.data.first.startTime);
    int adjustedPosition = widget.playbackPositionMillis;

    if (widget.audioDurationMillis > 0) {
      // Handle looping case
      final lastLyricEnd = _parseTimeToMillis(widget.lyricsData!.data.last.endTime);
      final lyricsDuration = lastLyricEnd > 0 ? lastLyricEnd : widget.audioDurationMillis;
      adjustedPosition = widget.playbackPositionMillis % lyricsDuration;
    }

    return adjustedPosition < firstLyricStart;
  }

  double _getCurrentLineHeight(String text) {
    // Calculate height needed for current line text
    // Use a simple estimation based on text length and typical character width
    if (text.isEmpty) return _minCurrentLineHeight;

    // Estimate lines needed based on text length (assuming ~25 chars per line for the font size)
    final estimatedLines = (text.length / 25).ceil().clamp(1, 3);

    // Base height + additional height for extra lines
    final baseHeight = _minCurrentLineHeight;
    final additionalHeight = (estimatedLines - 1) * 20; // 20px per additional line

    return baseHeight + additionalHeight;
  }

  double _getItemHeight(int lyricIndex, int currentIndex) {
    if (lyricIndex == currentIndex) {
      final text = lyricIndex < widget.lyrics.length ? widget.lyrics[lyricIndex] : '';
      return _getCurrentLineHeight(text);
    }
    return _otherLineHeight;
  }

  double _calculateScrollOffset(int targetIndex) {
    double offset = _otherLineHeight; // Start with the empty placeholder height

    // Calculate cumulative height up to target index
    for (int i = 0; i < targetIndex; i++) {
      offset += _getItemHeight(i, targetIndex);
    }

    return offset;
  }

  void _scrollToLyric(int index) {
    if (_previousLyricIndex != index && _scrollController.hasClients) {
      _previousLyricIndex = index;

      // Calculate the scroll offset to position the current line in the center
      double targetScrollOffset = _calculateScrollOffset(index);

      // Adjust to center the current line in the visible area
      // We want the current line to be in the middle position (after the first prev line)
      targetScrollOffset -= _otherLineHeight; // Account for the top prev line space

      // Ensure we don't scroll beyond bounds
      final totalContentHeight = _calculateScrollOffset(widget.lyrics.length) + _otherLineHeight; // Include final placeholder
      final currentText = index < widget.lyrics.length ? widget.lyrics[index] : '';
      final currentHeight = _getCurrentLineHeight(currentText);
      final visibleHeight = _otherLineHeight + currentHeight + _otherLineHeight;
      final maxScroll = (totalContentHeight - visibleHeight).clamp(0.0, double.infinity);

      targetScrollOffset = targetScrollOffset.clamp(0.0, maxScroll);

      // Smooth scroll to the target position
      _scrollController.animateTo(
        targetScrollOffset,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // if (widget.lyrics.isEmpty) {
    //   return MediaQuery(
    //     data: MediaQuery.of(context).copyWith(textScaler: TextScaler.noScaling),
    //     child: Center(
    //       child: Text(
    //         'No lyrics available',
    //         style: AppTextStyles.text16regular.copyWith(
    //           color: Colors.grey,
    //         ),
    //       ),
    //     ),
    //   );
    // }

    final currentIndex = _getCurrentLyricIndex();

    // Calculate total height for 3 lines (prev, current, next)
    final currentText = currentIndex < widget.lyrics.length ? widget.lyrics[currentIndex] : '';
    final currentHeight = _getCurrentLineHeight(currentText);
    final totalHeight = _otherLineHeight + currentHeight + _otherLineHeight;

    return MediaQuery(
      data: MediaQuery.of(context).copyWith(textScaler: TextScaler.noScaling),
      child: SizedBox(
        height: totalHeight,
        width: 240,
        child: Stack(
          children: [
            // Clipped container to show only 3 lines
            ClipRect(
              child: SizedBox(
                height: totalHeight,
                child: ListView.builder(
                  controller: _scrollController,
                  itemCount: widget.lyrics.length + 2, // Add 2 items: one empty at start, one at end
                  physics: const NeverScrollableScrollPhysics(), // Disable manual scrolling
                  padding: EdgeInsets.zero, // No padding, we'll control positioning manually
                  itemBuilder: (context, index) {
                    // First item: empty placeholder for proper spacing
                    if (index == 0) {
                      return SizedBox(
                        height: _otherLineHeight,
                        child: const SizedBox.shrink(), // Empty space
                      );
                    }
                    // Last item: empty placeholder for proper spacing
                    else if (index == widget.lyrics.length + 1) {
                      return SizedBox(
                        height: _otherLineHeight,
                        child: const SizedBox.shrink(), // Empty space
                      );
                    }
                    // Actual lyrics (adjusted index)
                    else {
                      final lyricIndex = index - 1;
                      final isCurrent = lyricIndex == currentIndex;
                      final itemHeight = isCurrent ? _getCurrentLineHeight(widget.lyrics[lyricIndex]) : _otherLineHeight;

                      return SizedBox(
                        height: itemHeight,
                        child: _buildLyricLine(
                          text: widget.lyrics[lyricIndex],
                          isCurrent: isCurrent,
                          isVisible: true,
                        ),
                      );
                    }
                  },
                ),
              ),
            ),

            // Top gradient mask - positioned over the top lyric line
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              height: _otherLineHeight,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withValues(alpha: 0.85),
                      Colors.black.withValues(alpha: 0.3),
                      Colors.transparent,
                    ],
                    stops: const [0.0, 0.7, 1.0],
                  ),
                ),
              ),
            ),

            // Bottom gradient mask - positioned over the bottom lyric line
            Positioned(
              bottom: 50,
              left: 0,
              right: 0,
              height: _otherLineHeight + 50,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    colors: [
                      Colors.black.withValues(alpha: 0.85),
                      Colors.black.withValues(alpha: 0.5),
                      Colors.black.withValues(alpha: 0.4),
                      Colors.black.withValues(alpha: 0.3),
                      Colors.black.withValues(alpha: 0.1),
                      Colors.transparent,
                    ],
                    stops: const [0.0, 0.3, 0.7, 0.9, 0.95, 1.0],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLyricLine({
    required String text,
    required bool isCurrent,
    required bool isVisible,
  }) {
    if (!isVisible || text.isEmpty) {
      return SizedBox(height: isCurrent ? _getCurrentLineHeight(text) : _otherLineHeight);
    }

    final padding = isCurrent ? _currentLinePadding : _otherLinePadding;

    if (isCurrent) {
      // Current line: no restrictions, show full text
      // Use smaller font size during idle time
      final isIdle = _isInIdleTime();
      final fontSize = isIdle ? 12.0 : 14.0;

      return Center(
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: padding),
          child: Text(
            text,
            textAlign: TextAlign.center,
            style: AppTextStyles.textEthnocentricStyle.copyWith(
              fontSize: fontSize,
              color: const Color(0xFFEBC0E8),
              fontWeight: FontWeight.normal,
              height: 1.2,
            ),
          ),
        ),
      );
    } else {
      // Other lines: max 2 lines with clipping (no ellipsis)
      return Center(
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: padding),
          child: ClipRect(
            child: SizedBox(
              height: _otherLineHeight - (padding * 2), // Available height minus padding
              child: Text(
                text,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.clip, // Clip instead of ellipsis
                style: AppTextStyles.textEthnocentricStyle.copyWith(
                  fontSize: 9,
                  color: Colors.white.withValues(alpha: 0.7),
                  fontWeight: FontWeight.normal,
                  height: 1.2,
                ),
              ),
            ),
          ),
        ),
      );
    }
  }
}
