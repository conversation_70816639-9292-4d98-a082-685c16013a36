import 'package:melodyze/core/ui/molecules/lyric_viewer/model/lyrics_data.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/api_client/api_client.dart';
import 'package:melodyze/core/wrappers/injector.dart';

class LyricsService {
  static Future<LyricsData?> fetchLyrics(String lyricsJsonPath) async {
    if (lyricsJsonPath.isEmpty) return null;
    
    try {
      final response = await DI().resolve<ApiClient>().get(lyricsJsonPath);
      if (response != null) {
        final lyricsData = LyricsData.fromJson(response);
        return lyricsData;
      }
    } catch (e) {
      logger.e('Failed to fetch lyrics: $e');
    }
    
    return null;
  }
}